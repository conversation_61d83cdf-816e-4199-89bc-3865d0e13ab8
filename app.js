const express = require('express');
const cors = require('cors');
const { createClerkClient } = require('@clerk/backend');
const { ConvexHttpClient } = require('convex/browser');
const { api } = require('./convex/_generated/api');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();

// Middleware
app.use(cors()); // Enable CORS for all routes
app.use(express.json()); // Parse JSON bodies

// Create Clerk client
const clerkClient = createClerkClient({
  secretKey: process.env.CLERK_SECRET_KEY,
  publishableKey: process.env.CLERK_PUBLISHABLE_KEY,
});

// Initialize Convex client
const convex = new ConvexHttpClient(process.env.PUBLIC_CONVEX_URL);

// Middleware to authenticate requests
const authenticateRequest = async (req, res, next) => {
  try {
    // Get the authorization header from the request
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header missing' });
    }

    // Extract the token from the Bearer header
    const token = authHeader.replace('Bearer ', '');

    // Create a mock Request object for Clerk's authenticateRequest
    const fullUrl = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
    const mockRequest = new Request(fullUrl, {
      method: req.method,
      headers: req.headers,
    });

    // Authenticate the request using Clerk
    const client = await clerkClient.authenticateRequest(mockRequest, {
      secretKey: process.env.CLERK_SECRET_KEY,
      publishableKey: process.env.CLERK_PUBLISHABLE_KEY,
    });

    // Get the userId from the authentication response
    const toAuth = await client.toAuth();
    const userId = toAuth?.userId;

    // If no userId is returned, the request is not authenticated
    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Add userId to request object for use in route handlers
    req.userId = userId;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({ error: 'Authentication failed' });
  }
};

// Protected route that requires authentication
app.get('/protected', authenticateRequest, async (req, res) => {
  try {
    // Get the authorization header to pass to Convex
    const authHeader = req.headers.authorization;
    const token = authHeader.replace('Bearer ', '');

    // Set the authentication token for Convex
    convex.setAuth(token);

    // Fetch the user's contacts from Convex
    const contacts = await convex.query(api.contacts.getContacts);

    console.log('User ID:', req.userId);
    console.log('Contacts fetched:', contacts.length);

    res.json({
      message: 'Access granted to protected route',
      userId: req.userId,
      contacts: contacts,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching contacts:', error);
    res.status(500).json({
      error: 'Failed to fetch contacts',
      message: error.message
    });
  }
});

// Public route (no authentication required)
app.get('/public', (req, res) => {
  res.json({ message: 'This is a public route' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Start the server
const PORT = 8787;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

module.exports = app;