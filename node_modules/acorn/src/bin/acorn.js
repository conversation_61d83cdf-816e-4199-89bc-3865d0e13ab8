#!/usr/bin/env node

import {basename} from "path"
import {readFileSync as readFile} from "fs"
import * as acorn from "../dist/acorn.js"

let infile, forceFile, silent = false, compact = false, tokenize = false
const options = {}

function help(status) {
  const print = (status == 0) ? console.log : console.error
  print("usage: " + basename(process.argv[1]) + " [--ecma3|--ecma5|--ecma6]")
  print("        [--tokenize] [--locations] [---allow-hash-bang] [--compact] [--silent] [--module] [--help] [--] [infile]")
  process.exit(status)
}

for (let i = 2; i < process.argv.length; ++i) {
  const arg = process.argv[i]
  if ((arg == "-" || arg[0] != "-") && !infile) infile = arg
  else if (arg == "--" && !infile && i + 2 == process.argv.length) forceFile = infile = process.argv[++i]
  else if (arg == "--ecma3") options.ecmaVersion = 3
  else if (arg == "--ecma5") options.ecmaVersion = 5
  else if (arg == "--ecma6") options.ecmaVersion = 6
  else if (arg == "--locations") options.locations = true
  else if (arg == "--allow-hash-bang") options.allowHashBang = true
  else if (arg == "--silent") silent = true
  else if (arg == "--compact") compact = true
  else if (arg == "--help") help(0)
  else if (arg == "--tokenize") tokenize = true
  else if (arg == "--module") options.sourceType = 'module'
  else help(1)
}

function run(code) {
  let result
  if (!tokenize) {
    try { result = acorn.parse(code, options) }
    catch(e) { console.error(e.message); process.exit(1) }
  } else {
    result = []
    let tokenizer = acorn.tokenizer(code, options), token
    while (true) {
      try { token = tokenizer.getToken() }
      catch(e) { console.error(e.message); process.exit(1) }
      result.push(token)
      if (token.type == acorn.tokTypes.eof) break
    }
  }
  if (!silent) console.log(JSON.stringify(result, null, compact ? null : 2))
}

if (forceFile || infile && infile != "-") {
  run(readFile(infile, "utf8"))
} else {
  let code = ""
  process.stdin.resume()
  process.stdin.on("data", chunk => code += chunk)
  process.stdin.on("end", () => run(code))
}
